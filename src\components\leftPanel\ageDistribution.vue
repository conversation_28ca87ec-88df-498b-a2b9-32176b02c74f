<!-- 游客年龄分布与年度接待游客比 -->
<template>
  <CPanel class="tourist-analysis">
    <template #header>游客分析统计</template>
    <template #content>
      <div class="tourist-content">
        <!-- 上半部分：数据指标 -->
        <div class="data-indicators">
          <div class="indicator-item">
            <div class="icon-container">
              <div class="icon-circle">
                <i class="icon-age"></i>
              </div>
            </div>
            <div class="indicator-content">
              <span class="indicator-label">主要年龄段</span>
              <span class="indicator-value">20-30岁</span>
            </div>
          </div>
          <div class="indicator-item">
            <div class="icon-container">
              <div class="icon-circle">
                <i class="icon-trend"></i>
              </div>
            </div>
            <div class="indicator-content">
              <span class="indicator-label">年度增长率</span>
              <span class="indicator-value">+12.5%</span>
            </div>
          </div>
        </div>

        <!-- 下半部分：图表区域 -->
        <div class="charts-section">
          <!-- 年龄分布图表 -->
          <div class="chart-container">
            <div class="chart-title">
              <span class="title-icon">◆</span>
              <span class="title-text">年龄分布统计</span>
            </div>
            <div class="chart-wrapper">
              <CEcharts ref="ageChartRef" :option="ageOption" @onload="startHighlightLoop" />
            </div>
          </div>

          <!-- 年度接待游客比图表 -->
          <div class="chart-container">
            <div class="chart-title">
              <span class="title-icon">◆</span>
              <span class="title-text">年度接待游客比</span>
            </div>
            <div class="chart-wrapper">
              <CEcharts ref="receptionChartRef" :option="receptionOption" />
            </div>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'
import type { EChartsOption, TooltipComponentOption, CustomSeriesOption, BarSeriesOption } from 'echarts'

const ageOption = ref<EChartsOption>({})
const receptionOption = ref<any>({})
const ageChartRef = ref()
const receptionChartRef = ref()
let highlightTimer: any = null
let currentIndex = 0
const values: number[] = [2000, 1430, 800, 410, 120]
// 高亮循环方法
const startHighlightLoop = (chart: any) => {
  if (!chart) return

  // 如果已经存在定时器，先清除
  if (highlightTimer) {
    clearInterval(highlightTimer)
    highlightTimer = null
  }

  highlightTimer = setInterval(() => {
    // 取消之前的高亮
    chart.dispatchAction({
      type: 'downplay'
    })
    // 高亮当前柱子
    chart.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: currentIndex
    })
    // 更新索引，循环
    currentIndex = (currentIndex + 1) % values.length
  }, 1500)
}

const createEchartBar = (): EChartsOption => {
  const offsetX = 10
  const offsetY = 5
  // 创建左侧面
  const CubeLeft = echarts.graphic.extendShape({
    shape: {
      x: 0,
      y: 0
    },
    buildPath: function (ctx: any, shape: any) {
      const xAxisPoint = shape.xAxisPoint
      const c0 = [shape.x, shape.y]
      const c1 = [shape.x - offsetX, shape.y - offsetY]
      const c2 = [xAxisPoint[0] - offsetX, xAxisPoint[1]]
      const c3 = [xAxisPoint[0], xAxisPoint[1]]
      ctx.moveTo(c0[0], c0[1])
      ctx.lineTo(c1[0], c1[1])
      ctx.lineTo(c2[0], c2[1])
      ctx.lineTo(c3[0], c3[1])
      ctx.closePath()
    }
  })
  // 绘制右侧面
  const CubeRight = echarts.graphic.extendShape({
    shape: {
      x: 0,
      y: 0
    },
    buildPath: function (ctx: any, shape: any) {
      const xAxisPoint = shape.xAxisPoint
      const c1 = [shape.x, shape.y]
      const c2 = [xAxisPoint[0], xAxisPoint[1]]
      const c3 = [xAxisPoint[0] + offsetX, xAxisPoint[1]]
      const c4 = [shape.x + offsetX, shape.y - offsetY]
      ctx.moveTo(c1[0], c1[1])
      ctx.lineTo(c2[0], c2[1])
      ctx.lineTo(c3[0], c3[1])
      ctx.lineTo(c4[0], c4[1])
      ctx.closePath()
    }
  })
  // 绘制顶面
  const CubeTop = echarts.graphic.extendShape({
    shape: {
      x: 0,
      y: 0
    },
    buildPath: function (ctx: any, shape: any) {
      const c1 = [shape.x, shape.y]
      const c2 = [shape.x + offsetX, shape.y - offsetY] //右点
      const c3 = [shape.x, shape.y - offsetX]
      const c4 = [shape.x - offsetX, shape.y - offsetY]
      ctx.moveTo(c1[0], c1[1])
      ctx.lineTo(c2[0], c2[1])
      ctx.lineTo(c3[0], c3[1])
      ctx.lineTo(c4[0], c4[1])
      ctx.closePath()
    }
  })
  // 注册三个面图形
  echarts.graphic.registerShape('CubeLeft', CubeLeft)
  echarts.graphic.registerShape('CubeRight', CubeRight)
  echarts.graphic.registerShape('CubeTop', CubeTop)

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function (params: any) {
        const item = params[1]
        return item.name + ' : ' + item.value
      }
    } as TooltipComponentOption,
    grid: {
      left: '0%',
      right: '0%',
      top: '20%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['20以下', '20-30', '30-40', '40-50', '50以上'],
      axisLine: {
        show: true,
        lineStyle: {
          width: 2,
          color: 'rgba(76, 93, 130, 1)'
        }
      },

      axisTick: {
        show: false
      },
      axisLabel: {
        fontSize: 12,
        color: 'rgba(201, 211, 234, 1)'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      name: '万人',
      nameTextStyle: {
        color: 'rgba(201, 211, 234, 1)',
        fontSize: 14,
        padding: [0, 32, 12, 0]
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(49, 58, 86, 1)'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        fontSize: 14,
        color: 'rgba(201, 211, 234, 1)'
      }
    },
    series: [
      {
        type: 'custom',
        renderItem: (_params: any, api: any) => {
          const location = api.coord([api.value(0), api.value(1)])
          return {
            type: 'group',
            children: [
              {
                type: 'CubeLeft',
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0])
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: 'rgba(114, 138, 192, 1)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(68, 95, 156, 1)'
                    }
                  ])
                },
                // hover样式
                emphasis: {
                  style: {
                    fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: 'rgba(230, 165, 75, 1)'
                      },
                      {
                        offset: 1,
                        color: 'rgba(175, 111, 23, 1)'
                      }
                    ])
                  }
                }
              },
              {
                type: 'CubeRight',
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0])
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: 'rgba(161, 186, 244, 1)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(104, 134, 202, 1)'
                    }
                  ])
                },
                emphasis: {
                  style: {
                    fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: 'rgba(230, 165, 75, 1)'
                      },
                      {
                        offset: 1,
                        color: 'rgba(175, 111, 23, 1)'
                      }
                    ])
                  }
                }
              },
              {
                type: 'CubeTop',
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0])
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: 'rgba(198, 213, 244, 1)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(198, 213, 244, 1)'
                    }
                  ])
                },
                emphasis: {
                  style: {
                    fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: 'rgba(230, 165, 75, 1)'
                      },
                      {
                        offset: 1,
                        color: 'rgba(230, 165, 75, 1)'
                      }
                    ])
                  }
                }
              }
            ]
          }
        },
        data: values
      } as unknown as CustomSeriesOption,
      {
        type: 'bar',
        label: {
          normal: {
            show: true,
            position: 'top',
            fontSize: 14,
            color: 'rgba(201, 211, 234, 1)',
            offset: [0, -25]
          }
        },
        itemStyle: {
          color: 'transparent'
        },
        tooltip: {},
        data: values
      } as BarSeriesOption
    ]
  }
}
// 创建年度接待游客比图表
const createReceptionChart = () => {
  return {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      top: '5%',
      right: '2%',
      itemGap: 20,
      itemWidth: 15,
      itemHeight: 1,
      textStyle: {
        color: '#C5D6E6',
        fontSize: 10
      }
    },
    grid: {
      left: '8%',
      right: '5%',
      bottom: '15%',
      top: '25%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
        axisLabel: {
          textStyle: {
            color: '#C5D6E6',
            fontSize: 10
          }
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(76, 93, 130, 1)'
          }
        },
        axisTick: {
          show: false
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '万人',
        nameTextStyle: {
          color: 'rgba(201, 211, 234, 1)',
          fontSize: 10,
          padding: [0, 20, 8, 0]
        },
        splitNumber: 3,
        splitLine: {
          lineStyle: {
            color: 'rgba(52, 71, 112, 1)',
            type: 'dashed'
          }
        },
        axisLabel: {
          textStyle: {
            color: '#C5D6E6',
            fontSize: 10
          }
        },
        axisLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: '2021年',
        type: 'line',
        data: [23, 60, 20, 36, 23, 85, 70, 60, 78, 89, 68, 56],
        lineStyle: {
          normal: {
            width: 2,
            color: 'rgba(218, 163, 88, 1)',
            shadowColor: 'rgba(218, 163, 88, 0.3)',
            shadowBlur: 10,
            shadowOffsetY: 20
          }
        },
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: 'rgba(218, 163, 88, 0.3)'
                },
                {
                  offset: 1,
                  color: 'rgba(218, 163, 88, 0)'
                }
              ],
              false
            )
          }
        },
        itemStyle: {
          color: 'rgba(15, 222, 255, 1)'
        },
        smooth: true,
        symbol: 'none'
      },
      {
        name: '2022年',
        type: 'line',
        data: [145, 78, 88, 99, 36, 109, 120, 150, 99, 89, 100, 120],
        lineStyle: {
          normal: {
            width: 2,
            color: 'rgba(109, 128, 175, 1)'
          }
        },
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: 'rgba(109, 128, 175, 0.3)'
                },
                {
                  offset: 1,
                  color: 'rgba(109, 128, 175, 0)'
                }
              ],
              false
            )
          }
        },
        itemStyle: {
          color: 'rgba(109, 128, 175, 1)'
        },
        smooth: true,
        symbol: 'none'
      }
    ]
  }
}

onMounted(() => {
  ageOption.value = createEchartBar()
  receptionOption.value = createReceptionChart()
})
onUnmounted(() => {
  if (highlightTimer) {
    clearInterval(highlightTimer)
  }
})
</script>

<style lang="scss" scoped>
.tourist-analysis {
  .tourist-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
    background: url('@/assets/img/dy_bg.png') no-repeat center center;
    background-size: 100% 100%;
  }

  .data-indicators {
    display: flex;
    gap: 16px;
    height: 80px;

    .indicator-item {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 16px;
      background: linear-gradient(135deg, rgba(45, 74, 123, 0.3) 0%, rgba(25, 54, 103, 0.3) 100%);
      border: 1px solid rgba(76, 93, 130, 0.5);
      border-radius: 8px;
      backdrop-filter: blur(10px);

      .icon-container {
        .icon-circle {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: linear-gradient(135deg, rgba(15, 222, 255, 0.2) 0%, rgba(109, 128, 175, 0.2) 100%);
          border: 2px solid rgba(15, 222, 255, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;

          &::before {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            background: rgba(15, 222, 255, 0.8);
            border-radius: 50%;
          }
        }
      }

      .indicator-content {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .indicator-label {
          font-size: 12px;
          color: rgba(201, 211, 234, 0.8);
          line-height: 1;
        }

        .indicator-value {
          font-size: 16px;
          font-weight: bold;
          color: rgba(15, 222, 255, 1);
          line-height: 1;
        }
      }
    }
  }

  .charts-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;

    .chart-container {
      flex: 1;
      display: flex;
      flex-direction: column;

      .chart-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;

        .title-icon {
          color: rgba(15, 222, 255, 1);
          font-size: 12px;
        }

        .title-text {
          font-size: 14px;
          color: rgba(201, 211, 234, 1);
          font-weight: 500;
        }
      }

      .chart-wrapper {
        flex: 1;
        min-height: 120px;
      }
    }
  }
}
</style>
